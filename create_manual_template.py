#!/usr/bin/env python3
"""
Create a manual template with the correct structure based on the assessment tools
"""

import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

def create_assessment_template():
    """Create Excel template with proper structure"""
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # Define styles
    title_font = Font(bold=True, size=14, color="FFFFFF")
    header_font = Font(bold=True, size=11, color="FFFFFF")
    item_font = Font(size=10)
    code_font = Font(size=10, bold=True)
    
    title_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    evaluation_fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
    
    border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                   top=Side(style='thin'), bottom=Side(style='thin'))
    
    # Sample data for Document Review Tool - Surveyor 1
    doc_review_s1_data = [
        ("LD.1.1", "The mission, vision, and values are approved and periodically reviewed by the governing body, and are made public."),
        ("LD.1.2", "The scope of services, plans, programs, and key policies and procedures are approved by the governing body."),
        ("LD.1.3", "The operating and capital budgets, and other resources required to execute the operational plan is approved by the governing body."),
        ("LD.1.4", "Approval authority delegation is defined by the governing body."),
        ("LD.2.1", "The quality and patient safety and risk management initiatives are annually approved by the governing body."),
        ("LD.2.2", "The quality and patient safety reports, corrective actions, and outcomes from the home healthcare service, including risk management activities, are reported to the governing body."),
        ("LD.3.1", "The governing body ensures that the home healthcare service has effective leadership."),
        ("LD.3.2", "The governing body defines the qualifications and responsibilities of the leadership role(s)."),
        ("LD.4.1", "The governing body ensures that the home healthcare service is in compliance with applicable laws and regulations."),
        ("LD.4.2", "The governing body ensures that the home healthcare service has a process to identify, monitor, and address compliance issues.")
    ]
    
    # Sample data for Medical Record Review Tool - Surveyor 1
    medical_review_s1_data = [
        ("MR.1.1", "Patient records are maintained in accordance with applicable laws and regulations."),
        ("MR.1.2", "Patient records contain required documentation elements."),
        ("MR.2.1", "Patient assessment is comprehensive and addresses all relevant aspects of care."),
        ("MR.2.2", "Patient care plans are individualized and based on assessment findings."),
        ("MR.3.1", "Patient care is documented in accordance with established policies."),
        ("MR.3.2", "Documentation supports the care provided and patient outcomes.")
    ]
    
    # Sample data for Personnel File Review Tool - Surveyor 1
    personnel_review_s1_data = [
        ("HR.1.1", "Personnel files contain required documentation for all staff."),
        ("HR.1.2", "Background checks are completed in accordance with applicable requirements."),
        ("HR.2.1", "Staff qualifications meet position requirements and applicable standards."),
        ("HR.2.2", "Competency assessments are conducted and documented."),
        ("HR.3.1", "Orientation programs address all required elements."),
        ("HR.3.2", "Ongoing education and training programs are provided and documented.")
    ]
    
    # Create sheets for all 6 tools
    tools_data = [
        ("Document Review - Surveyor 1", "Document Review Tool – For Surveyor 1", doc_review_s1_data),
        ("Medical Record Review - Surveyor 1", "Medical Record Review Tool – For Surveyor 1", medical_review_s1_data),
        ("Personnel File Review - Surveyor 1", "Personnel File Review Tool – For Surveyor 1", personnel_review_s1_data),
        ("Document Review - Surveyor 2", "Document Review Tool – For Surveyor 2", doc_review_s1_data),  # Same structure
        ("Medical Record Review - Surveyor 2", "Medical Record Review Tool – For Surveyor 2", medical_review_s1_data),  # Same structure
        ("Personnel File Review - Surveyor 2", "Personnel File Review Tool – For Surveyor 2", personnel_review_s1_data)  # Same structure
    ]
    
    for sheet_name, title, data in tools_data:
        ws = wb.create_sheet(title=sheet_name)
        
        # Add title
        ws['A1'] = title
        ws['A1'].font = title_font
        ws['A1'].fill = title_fill
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        ws.merge_cells('A1:F1')
        ws.row_dimensions[1].height = 25
        
        # Add column headers
        headers = ['#', 'Code', 'Sub-standard', 'Fully Met', 'Partially Met', 'Not Met']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        ws.row_dimensions[3].height = 20
        
        # Add assessment items
        for i, (code, description) in enumerate(data, 1):
            row = i + 3
            
            # Add row data
            ws.cell(row=row, column=1, value=i).font = item_font
            ws.cell(row=row, column=2, value=code).font = code_font
            ws.cell(row=row, column=3, value=description).font = item_font
            
            # Add evaluation columns with light background
            for col in range(4, 7):
                cell = ws.cell(row=row, column=col, value="")
                cell.fill = evaluation_fill
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # Apply borders to all cells
            for col in range(1, 7):
                ws.cell(row=row, column=col).border = border
        
        # Add summary row
        summary_row = len(data) + 5
        ws.cell(row=summary_row, column=1, value="TOTALS:").font = Font(bold=True)
        ws.cell(row=summary_row, column=4, value=f"=COUNTIF(D4:D{len(data)+3},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=5, value=f"=COUNTIF(E4:E{len(data)+3},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=6, value=f"=COUNTIF(F4:F{len(data)+3},\"X\")").font = Font(bold=True)
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 60
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 12
    
    return wb

def main():
    print("Creating manual assessment template...")
    wb = create_assessment_template()
    
    output_path = "HHS_Assessment_Tools_Template.xlsx"
    wb.save(output_path)
    print(f"Template saved as: {output_path}")
    
    print("\nTemplate includes:")
    print("- 6 sheets (3 tools × 2 surveyors)")
    print("- Proper code structure (LD.X.X, MR.X.X, HR.X.X)")
    print("- Evaluation columns: Fully Met, Partially Met, Not Met")
    print("- Summary formulas for counting responses")
    print("\nTo use: Mark 'X' in the appropriate evaluation column for each item")

if __name__ == "__main__":
    main()
