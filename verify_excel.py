#!/usr/bin/env python3
"""
Verify the created Excel file and show summary
"""

import openpyxl

def verify_excel_file(file_path):
    """Verify the Excel file and show summary"""
    try:
        wb = openpyxl.load_workbook(file_path)
        
        print(f"Excel file: {file_path}")
        print(f"Number of sheets: {len(wb.sheetnames)}")
        print("\nSheets created:")
        
        for i, sheet_name in enumerate(wb.sheetnames, 1):
            ws = wb[sheet_name]
            
            # Get title from A1
            title = ws['A1'].value if ws['A1'].value else "No title"
            
            # Count rows with data (excluding header rows)
            data_rows = 0
            for row in range(4, ws.max_row + 1):
                if ws.cell(row=row, column=1).value:
                    data_rows += 1
            
            print(f"{i}. {sheet_name}")
            print(f"   Title: {title}")
            print(f"   Assessment items: {data_rows}")
            print()
        
        wb.close()
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")

if __name__ == "__main__":
    verify_excel_file("HHS_Assessment_Tools_Final.xlsx")
