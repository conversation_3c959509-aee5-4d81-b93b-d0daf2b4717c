#!/usr/bin/env python3
"""
Add dropdown lists to the existing Excel file for easy selection
"""

import openpyxl
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill, Border, Side

def add_dropdowns_to_excel(input_file, output_file):
    """Add dropdown lists to evaluation columns"""
    
    # Load the existing workbook
    wb = openpyxl.load_workbook(input_file)
    
    # Define the dropdown options
    dropdown_options = '"Fully Met,Partially Met,Not Met"'
    
    # Process each sheet
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"Processing sheet: {sheet_name}")
        
        # Find the last row with data
        last_row = ws.max_row
        data_start_row = 4  # Data starts from row 4
        
        # Find actual last row with content (excluding empty rows)
        actual_last_row = data_start_row
        for row in range(data_start_row, last_row + 1):
            if ws.cell(row=row, column=1).value:  # Check if # column has value
                actual_last_row = row
        
        print(f"  Data rows: {data_start_row} to {actual_last_row}")
        
        # Create data validation for dropdown
        dv = DataValidation(type="list", formula1=dropdown_options, allow_blank=True)
        dv.error = 'Your entry is not in the list'
        dv.errorTitle = 'Invalid Entry'
        dv.prompt = 'Please select from the dropdown list'
        dv.promptTitle = 'Evaluation Selection'
        
        # Add the data validation to the worksheet
        ws.add_data_validation(dv)
        
        # Apply dropdown to evaluation columns (D, E, F) for all data rows
        for row in range(data_start_row, actual_last_row + 1):
            for col in ['D', 'E', 'F']:  # Fully Met, Partially Met, Not Met columns
                cell_ref = f"{col}{row}"
                dv.add(cell_ref)
                
                # Clear any existing content and set alignment
                cell = ws[cell_ref]
                cell.value = ""
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Update the summary formulas to count the new dropdown values
        summary_row = actual_last_row + 2
        
        # Clear existing summary if it exists
        for col in range(1, 7):
            ws.cell(row=summary_row, column=col).value = ""
        
        # Add new summary with updated formulas
        ws.cell(row=summary_row, column=1, value="TOTALS:").font = Font(bold=True)
        ws.cell(row=summary_row, column=2, value="Count:").font = Font(bold=True)
        
        # Count formulas for dropdown values
        ws.cell(row=summary_row, column=4, value=f'=COUNTIF(D{data_start_row}:D{actual_last_row},"Fully Met")').font = Font(bold=True)
        ws.cell(row=summary_row, column=5, value=f'=COUNTIF(E{data_start_row}:E{actual_last_row},"Partially Met")').font = Font(bold=True)
        ws.cell(row=summary_row, column=6, value=f'=COUNTIF(F{data_start_row}:F{actual_last_row},"Not Met")').font = Font(bold=True)
        
        # Add percentage row
        percentage_row = summary_row + 1
        ws.cell(row=percentage_row, column=1, value="PERCENTAGES:").font = Font(bold=True)
        ws.cell(row=percentage_row, column=2, value="(%)").font = Font(bold=True)
        
        total_items = actual_last_row - data_start_row + 1
        ws.cell(row=percentage_row, column=4, value=f'=ROUND(D{summary_row}/{total_items}*100,1)&"%"').font = Font(bold=True)
        ws.cell(row=percentage_row, column=5, value=f'=ROUND(E{summary_row}/{total_items}*100,1)&"%"').font = Font(bold=True)
        ws.cell(row=percentage_row, column=6, value=f'=ROUND(F{summary_row}/{total_items}*100,1)&"%"').font = Font(bold=True)
        
        print(f"  Added dropdowns to {actual_last_row - data_start_row + 1} rows")
        print(f"  Summary at row {summary_row}, Percentages at row {percentage_row}")
    
    # Save the updated workbook
    wb.save(output_file)
    wb.close()
    print(f"\nUpdated Excel file saved as: {output_file}")

def main():
    input_file = "HHS_Assessment_Tools_Final.xlsx"
    output_file = "HHS_Assessment_Tools_With_Dropdowns.xlsx"
    
    print("Adding dropdown lists to Excel file...")
    print("This will allow users to click and select evaluation options easily.")
    
    try:
        add_dropdowns_to_excel(input_file, output_file)
        
        print("\n" + "="*60)
        print("SUCCESS! Dropdown lists have been added.")
        print("="*60)
        print("\nHow to use:")
        print("1. Open the Excel file")
        print("2. Click on any cell in the evaluation columns (Fully Met, Partially Met, Not Met)")
        print("3. A dropdown arrow will appear - click it to select an option")
        print("4. The summary rows will automatically count and calculate percentages")
        print("\nFeatures added:")
        print("✓ Dropdown lists in all evaluation columns")
        print("✓ Automatic counting of selections")
        print("✓ Percentage calculations")
        print("✓ Data validation to prevent invalid entries")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
