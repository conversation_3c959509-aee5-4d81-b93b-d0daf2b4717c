#!/usr/bin/env python3
"""
Clean up numeric values from the end of description rows
"""

import openpyxl
import re

def clean_descriptions(input_file, output_file):
    """Remove unwanted numeric values from descriptions"""
    
    # Load the existing workbook
    wb = openpyxl.load_workbook(input_file)
    
    print("Cleaning numeric values from descriptions...")
    
    # Process each sheet
    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]
        print(f"Processing sheet: {sheet_name}")
        
        # Find the last row with data
        last_row = ws.max_row
        data_start_row = 4  # Data starts from row 4
        
        # Find actual last row with content (excluding empty rows)
        actual_last_row = data_start_row
        for row in range(data_start_row, last_row + 1):
            if ws.cell(row=row, column=1).value:  # Check if # column has value
                actual_last_row = row
        
        cleaned_count = 0
        
        # Clean descriptions in column C (Sub-standard)
        for row in range(data_start_row, actual_last_row + 1):
            cell = ws.cell(row=row, column=3)  # Column C = Sub-standard
            if cell.value:
                original_text = str(cell.value)
                
                # Remove numeric values at the end of text
                # This will remove patterns like: "text 123", "text 45.6", "text 1 2 3", etc.
                cleaned_text = re.sub(r'\s+\d+(\.\d+)?\s*$', '', original_text)  # Remove single number at end
                cleaned_text = re.sub(r'\s+\d+(\s+\d+)*\s*$', '', cleaned_text)  # Remove multiple numbers at end
                cleaned_text = re.sub(r'\s+\d+\s*$', '', cleaned_text)  # Remove any remaining single digits
                
                # Remove any trailing punctuation that might be left
                cleaned_text = re.sub(r'\s*[,.\-–]\s*$', '', cleaned_text)
                
                # Clean up extra spaces
                cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
                
                if cleaned_text != original_text:
                    cell.value = cleaned_text
                    cleaned_count += 1
                    print(f"  Row {row}: Cleaned description")
        
        print(f"  Cleaned {cleaned_count} descriptions in {sheet_name}")
    
    # Save the updated workbook
    wb.save(output_file)
    wb.close()
    print(f"\nCleaned Excel file saved as: {output_file}")

def main():
    input_file = "HHS_Assessment_Tools_With_Dropdowns.xlsx"
    output_file = "HHS_Assessment_Tools_Clean.xlsx"
    
    print("Removing unwanted numeric values from descriptions...")
    
    try:
        clean_descriptions(input_file, output_file)
        
        print("\n" + "="*60)
        print("SUCCESS! Numeric values have been removed.")
        print("="*60)
        print("\nWhat was cleaned:")
        print("✓ Removed trailing numbers from descriptions")
        print("✓ Cleaned up extra spaces and punctuation")
        print("✓ Preserved all dropdown functionality")
        print("✓ Maintained all formatting and formulas")
        print(f"\nFinal file: {output_file}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
