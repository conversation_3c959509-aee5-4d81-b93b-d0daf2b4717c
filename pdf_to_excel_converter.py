#!/usr/bin/env python3
"""
PDF to Excel Converter for HHS Assessment Tools
Extracts assessment tools from PDF starting page 37 and converts to Excel format
"""

import PyPDF2
import openpyxl
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill, Border, Side
import re

def extract_pdf_text(pdf_path, start_page=36):
    """Extract text from PDF starting from specified page (0-indexed)"""
    text_content = []

    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            total_pages = len(pdf_reader.pages)

            print(f"Total pages in PDF: {total_pages}")
            print(f"Extracting from page {start_page + 1} to {total_pages}")

            for page_num in range(start_page, total_pages):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                text_content.append({
                    'page_number': page_num + 1,
                    'text': text
                })

                # Debug: Print first few lines of each page
                lines = text.split('\n')[:5]
                print(f"Page {page_num + 1} preview: {lines}")

    except Exception as e:
        print(f"Error reading PDF: {e}")
        return []

    return text_content

def parse_assessment_tools(text_content):
    """Parse the extracted text to identify assessment tools"""
    tools = []
    current_tool = None

    for page_data in text_content:
        text = page_data['text']
        lines = text.split('\n')

        for line in lines:
            line = line.strip()

            # Look for tool titles with more flexible matching
            if ('Document Review Tool' in line or 'Document Review' in line) and 'Surveyor' in line:
                if current_tool:
                    tools.append(current_tool)
                current_tool = {
                    'title': line,
                    'type': 'Document Review',
                    'surveyor': 'Surveyor 1' if 'Surveyor 1' in line or 'Surveyor  1' in line else 'Surveyor 2',
                    'items': [],
                    'page': page_data['page_number']
                }
                print(f"Found Document Review Tool: {line} on page {page_data['page_number']}")
            elif ('Medical Record Review Tool' in line or 'Medical Record Review' in line) and 'Surveyor' in line:
                if current_tool:
                    tools.append(current_tool)
                current_tool = {
                    'title': line,
                    'type': 'Medical Record Review',
                    'surveyor': 'Surveyor 1' if 'Surveyor 1' in line or 'Surveyor  1' in line else 'Surveyor 2',
                    'items': [],
                    'page': page_data['page_number']
                }
                print(f"Found Medical Record Review Tool: {line} on page {page_data['page_number']}")
            elif ('Personnel File Review Tool' in line or 'Personnel File Review' in line) and 'Surveyor' in line:
                if current_tool:
                    tools.append(current_tool)
                current_tool = {
                    'title': line,
                    'type': 'Personnel File Review',
                    'surveyor': 'Surveyor 1' if 'Surveyor 1' in line or 'Surveyor  1' in line else 'Surveyor 2',
                    'items': [],
                    'page': page_data['page_number']
                }
                print(f"Found Personnel File Review Tool: {line} on page {page_data['page_number']}")
            elif current_tool and line and not line.startswith('Page') and len(line) > 5:
                # Add content lines to current tool (filter out very short lines)
                current_tool['items'].append(line)

    # Add the last tool
    if current_tool:
        tools.append(current_tool)

    return tools

def create_excel_workbook(tools):
    """Create Excel workbook with assessment tools"""
    wb = openpyxl.Workbook()

    # Remove default sheet
    wb.remove(wb.active)

    # Define styles
    title_font = Font(bold=True, size=14, color="FFFFFF")
    header_font = Font(bold=True, size=11, color="FFFFFF")
    item_font = Font(size=10)
    code_font = Font(size=9, bold=True)

    title_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    evaluation_fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")

    border = Border(left=Side(style='thin'), right=Side(style='thin'),
                   top=Side(style='thin'), bottom=Side(style='thin'))

    for tool in tools:
        # Create sheet name (shortened to fit Excel limits)
        tool_type_short = tool['type'].replace(' Review', '').replace(' Tool', '')
        sheet_name = f"{tool_type_short} - {tool['surveyor']}"
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:31]

        ws = wb.create_sheet(title=sheet_name)

        # Add title
        ws['A1'] = tool['title']
        ws['A1'].font = title_font
        ws['A1'].fill = title_fill
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        ws.merge_cells('A1:F1')
        ws.row_dimensions[1].height = 25

        # Add column headers
        headers = ['#', 'Code', 'Sub-standard', 'Fully Met', 'Partially Met', 'Not Met']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')

        ws.row_dimensions[3].height = 20

        # Parse and add assessment items
        row = 4
        item_number = 1

        # Join all items into one text block for better parsing
        full_text = " ".join([item.strip() for item in tool['items'] if item.strip()])

        # Split by code patterns (LD.X.X, etc.)
        code_pattern = r'([A-Z]{1,3}\.\d+\.?\d*)\s+'
        parts = re.split(code_pattern, full_text)

        # Process the split parts
        for i in range(1, len(parts), 2):  # Skip first empty part, then take pairs
            if i + 1 < len(parts):
                code = parts[i].strip()
                description = parts[i + 1].strip()

                # Clean up description - remove extra spaces and line breaks
                description = re.sub(r'\s+', ' ', description)

                # Remove trailing punctuation that might be from next code
                description = re.sub(r'\s+[A-Z]{1,3}\.\d+\.?\d*.*$', '', description)

                if code and description and len(description) > 10:
                    # Add row data
                    ws.cell(row=row, column=1, value=item_number).font = item_font
                    ws.cell(row=row, column=2, value=code).font = code_font
                    ws.cell(row=row, column=3, value=description).font = item_font

                    # Add evaluation columns with light background
                    for col in range(4, 7):
                        cell = ws.cell(row=row, column=col, value="")
                        cell.fill = evaluation_fill
                        cell.border = border
                        cell.alignment = Alignment(horizontal='center', vertical='center')

                    # Apply borders to all cells
                    for col in range(1, 7):
                        ws.cell(row=row, column=col).border = border

                    row += 1
                    item_number += 1

        # Add summary row
        summary_row = row + 1
        ws.cell(row=summary_row, column=1, value="TOTALS:").font = Font(bold=True)
        ws.cell(row=summary_row, column=4, value=f"=COUNTIF(D4:D{row-1},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=5, value=f"=COUNTIF(E4:E{row-1},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=6, value=f"=COUNTIF(F4:F{row-1},\"X\")").font = Font(bold=True)

        # Adjust column widths
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 60
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 12

    return wb

def main():
    pdf_path = "HHS HAG.pdf"
    output_path = "HHS_Assessment_Tools_Final.xlsx"
    
    print("Starting PDF to Excel conversion...")
    
    # Extract text from PDF
    print("Extracting text from PDF...")
    text_content = extract_pdf_text(pdf_path, start_page=36)  # Page 37 is index 36
    
    if not text_content:
        print("Failed to extract text from PDF")
        return
    
    print(f"Extracted text from {len(text_content)} pages")
    
    # Parse assessment tools
    print("Parsing assessment tools...")
    tools = parse_assessment_tools(text_content)
    
    print(f"Found {len(tools)} assessment tools:")
    for tool in tools:
        print(f"  - {tool['title']} (Page {tool['page']})")
    
    # Create Excel workbook
    print("Creating Excel workbook...")
    wb = create_excel_workbook(tools)
    
    # Save workbook
    wb.save(output_path)
    print(f"Excel file saved as: {output_path}")

if __name__ == "__main__":
    main()
