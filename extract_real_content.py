#!/usr/bin/env python3
"""
Extract real content from PDF and create properly structured Excel
"""

import PyPDF2
import openpyxl
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill, Border, Side
import re

def extract_and_clean_text(pdf_path, start_page=36):
    """Extract and clean text from PDF"""
    all_text = ""
    
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num in range(start_page, len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                all_text += text + "\n"
                
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return ""
    
    return all_text

def parse_assessment_content(text):
    """Parse assessment content and extract codes and descriptions"""
    
    # Split text into sections for each tool
    tools = {}
    
    # Look for tool headers
    tool_patterns = [
        (r'Document Review\s*Tool\s*–\s*For Surveyor\s*1', 'Document Review - Surveyor 1'),
        (r'Medical Record Review\s*Tool\s*–\s*For Surveyor\s*1', 'Medical Record Review - Surveyor 1'),
        (r'Personnel File Review\s*Tool\s*–\s*For Surveyor\s*1', 'Personnel File Review - Surveyor 1'),
        (r'Document Review\s*Tool\s*–\s*For Surveyor\s*2', 'Document Review - Surveyor 2'),
        (r'Medical Record Review\s*Tool\s*–\s*For Surveyor\s*2', 'Medical Record Review - Surveyor 2'),
        (r'Personnel File Review\s*Tool\s*–\s*For Surveyor\s*2', 'Personnel File Review - Surveyor 2')
    ]
    
    # Find sections
    sections = []
    for pattern, name in tool_patterns:
        matches = list(re.finditer(pattern, text, re.IGNORECASE))
        for match in matches:
            sections.append((match.start(), name))
    
    sections.sort()
    
    # Extract content for each section
    for i, (start_pos, tool_name) in enumerate(sections):
        end_pos = sections[i + 1][0] if i + 1 < len(sections) else len(text)
        section_text = text[start_pos:end_pos]
        
        # Extract codes and descriptions
        items = []
        
        # Look for patterns like LD.1.1, MR.2.3, HR.1.2, etc.
        code_pattern = r'([A-Z]{1,3}\.\d+\.?\d*)\s+([^A-Z]{10,}?)(?=[A-Z]{1,3}\.\d+\.?\d*|$)'
        matches = re.findall(code_pattern, section_text, re.DOTALL)
        
        for code, description in matches:
            # Clean up description
            description = re.sub(r'\s+', ' ', description.strip())
            description = re.sub(r'^\s*[–-]\s*', '', description)  # Remove leading dashes
            description = description.strip()
            
            if len(description) > 10:  # Filter out very short descriptions
                items.append((code, description))
        
        if items:
            tools[tool_name] = items
    
    return tools

def create_excel_from_parsed_data(tools_data):
    """Create Excel workbook from parsed data"""
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # Define styles
    title_font = Font(bold=True, size=14, color="FFFFFF")
    header_font = Font(bold=True, size=11, color="FFFFFF")
    item_font = Font(size=10)
    code_font = Font(size=10, bold=True)
    
    title_fill = PatternFill(start_color="2F5597", end_color="2F5597", fill_type="solid")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    evaluation_fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
    
    border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                   top=Side(style='thin'), bottom=Side(style='thin'))
    
    for tool_name, items in tools_data.items():
        # Create sheet
        sheet_name = tool_name
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:31]
        
        ws = wb.create_sheet(title=sheet_name)
        
        # Add title
        title = f"{tool_name.replace(' - ', ' Tool – For ')}"
        ws['A1'] = title
        ws['A1'].font = title_font
        ws['A1'].fill = title_fill
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        ws.merge_cells('A1:F1')
        ws.row_dimensions[1].height = 25
        
        # Add column headers
        headers = ['#', 'Code', 'Sub-standard', 'Fully Met', 'Partially Met', 'Not Met']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        ws.row_dimensions[3].height = 20
        
        # Add assessment items
        for i, (code, description) in enumerate(items, 1):
            row = i + 3
            
            # Add row data
            ws.cell(row=row, column=1, value=i).font = item_font
            ws.cell(row=row, column=2, value=code).font = code_font
            ws.cell(row=row, column=3, value=description).font = item_font
            
            # Add evaluation columns with light background
            for col in range(4, 7):
                cell = ws.cell(row=row, column=col, value="")
                cell.fill = evaluation_fill
                cell.border = border
                cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # Apply borders to all cells
            for col in range(1, 7):
                ws.cell(row=row, column=col).border = border
        
        # Add summary row
        summary_row = len(items) + 5
        ws.cell(row=summary_row, column=1, value="TOTALS:").font = Font(bold=True)
        ws.cell(row=summary_row, column=4, value=f"=COUNTIF(D4:D{len(items)+3},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=5, value=f"=COUNTIF(E4:E{len(items)+3},\"X\")").font = Font(bold=True)
        ws.cell(row=summary_row, column=6, value=f"=COUNTIF(F4:F{len(items)+3},\"X\")").font = Font(bold=True)
        
        # Adjust column widths
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 60
        ws.column_dimensions['D'].width = 12
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 12
    
    return wb

def main():
    pdf_path = "HHS HAG.pdf"
    output_path = "HHS_Assessment_Tools_Extracted.xlsx"
    
    print("Extracting content from PDF...")
    text = extract_and_clean_text(pdf_path)
    
    print("Parsing assessment tools...")
    tools_data = parse_assessment_content(text)
    
    print(f"Found {len(tools_data)} tools:")
    for tool_name, items in tools_data.items():
        print(f"  - {tool_name}: {len(items)} items")
    
    if tools_data:
        print("Creating Excel workbook...")
        wb = create_excel_from_parsed_data(tools_data)
        wb.save(output_path)
        print(f"Excel file saved as: {output_path}")
    else:
        print("No assessment tools found. Using template instead.")

if __name__ == "__main__":
    main()
